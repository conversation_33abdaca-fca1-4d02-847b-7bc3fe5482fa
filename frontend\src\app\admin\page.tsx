'use client';

import {<PERSON>Header} from '@/components/ui/PageHeader';
import {Settings, PlusCircle, Info} from 'lucide-react';
import {SupabaseDiagnostics} from '@/components/admin/SupabaseDiagnostics';
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {Alert, AlertDescription, AlertTitle} from '@/components/ui/alert';
import {Tabs, TabsContent, TabsList, TabsTrigger} from '@/components/ui/tabs';
import {ActionButton} from '@/components/ui/action-button';

export default function AdminPage() {
	return (
		<div className='space-y-6'>
			<PageHeader
				title='Admin Dashboard'
				description='System administration and diagnostics'
				icon={Settings}
			/>

			<Alert>
				<Info className='h-4 w-4' />
				<AlertTitle>Information</AlertTitle>
				<AlertDescription>
					This admin dashboard provides system diagnostics and monitoring tools.
					No authentication is required for demonstration purposes.
				</AlertDescription>
			</Alert>

			<Tabs defaultValue='diagnostics' className='w-full'>
				<TabsList className='grid w-full grid-cols-1 md:grid-cols-3'>
					<TabsTrigger value='diagnostics'>Supabase Diagnostics</TabsTrigger>
					<TabsTrigger value='system'>System Status</TabsTrigger>
					<TabsTrigger value='docs'>Documentation</TabsTrigger>
				</TabsList>

				<TabsContent value='diagnostics' className='mt-6'>
					<SupabaseDiagnostics />
				</TabsContent>

				<TabsContent value='system' className='mt-6'>
					<Card className='shadow-md'>
						<CardHeader className='p-5'>
							<CardTitle className='text-xl font-semibold text-primary'>
								System Status
							</CardTitle>
							<CardDescription>
								Overview of system components and services
							</CardDescription>
						</CardHeader>
						<CardContent className='p-5'>
							<div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
								<Card className='shadow-sm'>
									<CardHeader className='pb-2 p-4'>
										<CardTitle className='text-base font-semibold'>
											Backend API
										</CardTitle>
										<CardDescription className='text-xs'>
											Node.js API Server
										</CardDescription>
									</CardHeader>
									<CardContent className='p-4 pt-0'>
										<div className='text-xl font-bold text-green-500'>
											Online
										</div>
										<p className='text-xs text-muted-foreground'>
											Version: 1.0.0
										</p>
									</CardContent>
								</Card>

								<Card className='shadow-sm'>
									<CardHeader className='pb-2 p-4'>
										<CardTitle className='text-base font-semibold'>
											Frontend
										</CardTitle>
										<CardDescription className='text-xs'>
											Next.js Application
										</CardDescription>
									</CardHeader>
									<CardContent className='p-4 pt-0'>
										<div className='text-xl font-bold text-green-500'>
											Online
										</div>
										<p className='text-xs text-muted-foreground'>
											Version: 1.0.0
										</p>
									</CardContent>
								</Card>

								<Card className='shadow-sm'>
									<CardHeader className='pb-2 p-4'>
										<CardTitle className='text-base font-semibold'>
											Socket Service
										</CardTitle>
										<CardDescription className='text-xs'>
											Real-time Updates
										</CardDescription>
									</CardHeader>
									<CardContent className='p-4 pt-0'>
										<div className='text-xl font-bold text-green-500'>
											Online
										</div>
										<p className='text-xs text-muted-foreground'>
											Active Connections: 3
										</p>
									</CardContent>
								</Card>
							</div>
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value='docs' className='mt-6'>
					<Card className='shadow-md'>
						<CardHeader className='p-5'>
							<CardTitle className='text-xl font-semibold text-primary'>
								Admin Documentation
							</CardTitle>
							<CardDescription>
								How to extend and customize the admin dashboard
							</CardDescription>
						</CardHeader>
						<CardContent className='space-y-4 p-5'>
							<div>
								<h3 className='text-lg font-semibold mb-2'>
									Extending the Admin Dashboard
								</h3>
								<p className='text-muted-foreground text-sm'>
									The admin dashboard is designed to be easily extensible.
									Follow these steps to add new features:
								</p>
								<ol className='list-decimal list-inside mt-2 space-y-1 text-muted-foreground text-sm'>
									<li>
										Create new component(s) in the{' '}
										<code className='bg-muted px-1 rounded'>
											frontend/src/components/admin
										</code>{' '}
										directory
									</li>
									<li>
										Add new tab(s) to the main admin page or to specific
										diagnostic sections
									</li>
									<li>Create new API endpoints in the backend if needed</li>
									<li>
										Update the admin service with new functions to fetch data
									</li>
								</ol>
							</div>

							<div>
								<h3 className='text-lg font-semibold mb-2'>
									Supabase Integration
								</h3>
								<p className='text-muted-foreground text-sm'>
									The Supabase diagnostics section communicates with the backend
									API, which then connects to Supabase. This follows the
									established pattern of not connecting directly to Supabase
									from the frontend.
								</p>
								<p className='text-muted-foreground mt-2 text-sm'>
									To add more Supabase-related features:
								</p>
								<ol className='list-decimal list-inside mt-2 space-y-1 text-muted-foreground text-sm'>
									<li>Add new endpoints to the backend API</li>
									<li>
										Create corresponding service functions in{' '}
										<code className='bg-muted px-1 rounded'>
											adminService.ts
										</code>
									</li>
									<li>Build new UI components that consume these services</li>
								</ol>
							</div>
						</CardContent>
						<CardFooter className='p-5'>
							<ActionButton
								actionType='tertiary'
								className='w-full'
								icon={<PlusCircle className='h-4 w-4' />}>
								Add New Feature (Placeholder)
							</ActionButton>
						</CardFooter>
					</Card>
				</TabsContent>
			</Tabs>
		</div>
	);
}
