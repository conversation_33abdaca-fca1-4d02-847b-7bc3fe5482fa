
"use client";

import VehicleForm from '@/components/vehicles/VehicleForm';
import { addVehicle as storeAddVehicle } from '@/lib/store';
import { useRouter } from 'next/navigation';
import type { Vehicle } from '@/lib/types';
import { PageHeader } from '@/components/ui/PageHeader';
import { CarIcon } from 'lucide-react';

export default function AddVehiclePage() {
  const router = useRouter();

  const handleSubmit = (data: Omit<Vehicle, 'id' | 'serviceHistory' | 'imageUrl'>) => {
    storeAddVehicle(data);
    router.push('/vehicles');
  };

  return (
    <div className="space-y-6">
      <PageHeader title="Add New Vehicle" description="Enter the details of your new vehicle." icon={CarIcon}/>
      <VehicleForm onSubmit={handleSubmit} />
    </div>
  );
}

