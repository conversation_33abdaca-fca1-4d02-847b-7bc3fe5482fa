# WorkHub Staging Environment - Security Enhanced Configuration
# Phase 1 Security Hardening Complete: Docker Security, Headers, Input Validation, Rate Limiting
version: '3.8'

services:
  # Backend API service - Staging Configuration with Phase 1 Security Hardening
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: runtime # Use security-hardened runtime stage
    image: workhub-backend:staging-security-hardened
    ports:
      - '3001:3001'
    environment:
      - NODE_ENV=staging
      - PORT=3001
      - USE_SUPABASE=true
      # Supabase Configuration (Production/Staging)
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - DATABASE_URL=${DATABASE_URL}
      # Security Configuration - Phase 1 Enhanced
      - JWT_SECRET=${JWT_SECRET}
      - LOG_LEVEL=info
      # CORS Configuration for staging
      - FRONTEND_URL=${FRONTEND_URL:-http://localhost:3000,https://your-staging-frontend.com}
    restart: unless-stopped
    # Enhanced health check with security verification
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3001/api/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # Security: Resource limits to prevent DoS
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
    # Security: Read-only root filesystem (where possible)
    read_only: false # Set to true if application supports it
    # Security: Drop unnecessary capabilities
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE # Only if binding to privileged ports
    # Security: No new privileges
    security_opt:
      - no-new-privileges:true
    networks:
      - workhub-staging

  # Frontend web application - Staging Configuration with Phase 1 Security Hardening
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: runtime # Use security-hardened runtime stage
      args:
        NEXT_PUBLIC_SUPABASE_URL: ${SUPABASE_URL}
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
        NEXT_PUBLIC_API_BASE_URL: http://backend:3001/api
        NEXT_PUBLIC_API_URL: http://localhost:3001
    image: workhub-frontend:staging-security-hardened
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=staging
      # Frontend Supabase Configuration
      - NEXT_PUBLIC_SUPABASE_URL=${SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      # API Configuration
      - NEXT_PUBLIC_API_BASE_URL=http://backend:3001/api
      - NEXT_PUBLIC_API_URL=http://localhost:3001
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    # Enhanced health check
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # Security: Resource limits to prevent DoS
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
    # Security: Read-only root filesystem (where possible)
    read_only: false # Set to true if application supports it
    # Security: Drop unnecessary capabilities
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE # Only if binding to privileged ports
    # Security: No new privileges
    security_opt:
      - no-new-privileges:true
    networks:
      - workhub-staging

  # Nginx reverse proxy for staging (optional but recommended)
  nginx:
    image: nginx:alpine
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - ./nginx/staging.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    networks:
      - workhub-staging

networks:
  workhub-staging:
    driver: bridge
    name: workhub-staging-network

volumes:
  staging_logs:
    name: workhub-staging-logs
